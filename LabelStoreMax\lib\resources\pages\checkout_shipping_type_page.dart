//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/app/models/checkout_session.dart';
import '/app/models/libyan_city.dart';
import '/app/services/libyan_delivery_service.dart';
import '/bootstrap/helpers.dart';
import '/resources/widgets/app_loader_widget.dart';
import '/resources/widgets/safearea_widget.dart';

import 'package:nylo_framework/nylo_framework.dart';


class CheckoutShippingTypePage extends NyStatefulWidget {
  static RouteView path =
      ("/checkout-shipping-type", (_) => CheckoutShippingTypePage());

  CheckoutShippingTypePage({super.key})
      : super(child: () => _CheckoutShippingTypePageState());
}

class _CheckoutShippingTypePageState extends NyPage<CheckoutShippingTypePage> {
  bool _isLoading = true;

  // Libya city selection variables
  LibyanCity? _selectedCity;
  List<LibyanCity> _libyanCities = [];
  bool _showCityDropdown = false;


  @override
  get init => () {
        _initializeLibyanCities();
        setState(() {
          _isLoading = false;
        });
      };

  /// Initialize Libyan cities data
  void _initializeLibyanCities() {
    _libyanCities = LibyanCitiesData.getAllCities();
    print('✅ Loaded ${_libyanCities.length} Libyan cities for shipping');
  }







  @override
  Widget view(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeColor.get(context).backgroundContainer, // FIX: Use themed color
      appBar: AppBar(
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        elevation: 0,
        title: Text(
          _getDynamicTitle(),
          style: TextStyle(
            color: ThemeColor.get(context).textPrimary, // FIX: Use themed color
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: ThemeColor.get(context).textPrimary), // FIX: Use themed color
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SafeAreaWidget(
        child: _isLoading
          ? Center(child: AppLoaderWidget())
          : _buildBeautifulShippingPage(),
      ),
    );
  }

  /// Build the beautiful shipping page matching the example image
  Widget _buildBeautifulShippingPage() {
    final checkoutSession = CheckoutSession.getInstance;
    bool hasShippingMethod = checkoutSession.shippingType != null;

    return SingleChildScrollView(
      padding: EdgeInsets.all(20),
      child: Column(
        children: [
          // Top illustration section
          Container(
            padding: EdgeInsets.all(20),
            margin: EdgeInsets.only(bottom: 30),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: ThemeColor.get(context).shadowLight,
                  blurRadius: 10,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Delivery illustration
                Container(
                  height: 140,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: ThemeColor.get(context).brandPrimary.withValues(alpha: 0.1), // DISTINCT: Light brand color for shipping
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Center(
                    child: Image.asset(
                      'public/images/shipping_page/shipping page illutration.png',
                      height: 100,
                      fit: BoxFit.contain,
                      errorBuilder: (context, error, stackTrace) => Icon(
                        Icons.local_shipping_outlined,
                        size: 50,
                        color: ThemeColor.get(context).brandPrimary,
                      ),
                    ),
                  ),
                ),
                SizedBox(height: 20),

                // Content section
                Text(
                  'اختر طريقة الشحن',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: ThemeColor.get(context).textPrimary, // FIX: Use themed color
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 8),
                Text(
                  'حدد المدينة لحساب تكلفة التوصيل',
                  style: TextStyle(
                    fontSize: 14,
                    color: ThemeColor.get(context).textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

          // Shipping method card
          _buildShippingMethodCard(hasShippingMethod, checkoutSession),

          SizedBox(height: 20),

          // City selection if needed
          if (_showCityDropdown) _buildBeautifulCityDropdown(),

          SizedBox(height: 30),

          // Cancel button
          _buildCancelButton(),
        ],
      ),
    );
  }



  /// Build the shipping method card matching the example design
  Widget _buildShippingMethodCard(bool hasShippingMethod, CheckoutSession checkoutSession) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: ThemeColor.get(context).shadowMedium,
            blurRadius: 20,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: hasShippingMethod ? _buildSelectedShippingMethod(checkoutSession) : _buildSelectCityPrompt(),
    );
  }

  /// Build selected shipping method display
  Widget _buildSelectedShippingMethod(CheckoutSession checkoutSession) {
    return Column(
      children: [
        // Green checkmark
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: ThemeColor.get(context).successColor, // FIX: Use themed color
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.check,
            color: ThemeColor.get(context).textOnPrimary,
            size: 30,
          ),
        ),
        SizedBox(height: 20),

        // Shipping method title
        Text(
          "التوصيل (درب السبيل)",
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: ThemeColor.get(context).textPrimary, // FIX: Use themed color
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 8),

        // Price
        Text(
          "السعر: ${formatStringCurrency(total: checkoutSession.shippingType!.cost)} د.ل",
          style: TextStyle(
            fontSize: 16,
            color: ThemeColor.get(context).textSecondary, // FIX: Use themed color
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// Build prompt to select city first
  Widget _buildSelectCityPrompt() {
    return Column(
      children: [
        // Info icon
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: ThemeColor.get(context).brandSecondary.withValues(alpha: 0.3), // FIX: Use themed color
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.location_city,
            color: ThemeColor.get(context).brandPrimary, // FIX: Use themed color
            size: 30,
          ),
        ),
        SizedBox(height: 20),

        // Message
        Text(
          "الرجاء تحديد مدينتك أولاً",
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: ThemeColor.get(context).textPrimary, // FIX: Use themed color
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 8),

        Text(
          "Please select your city first",
          style: TextStyle(
            fontSize: 14,
            color: ThemeColor.get(context).textSecondary, // FIX: Use themed color
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// Build beautiful city dropdown
  Widget _buildBeautifulCityDropdown() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: ThemeColor.get(context).shadowMedium,
            blurRadius: 20,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'اختر مدينتك',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: ThemeColor.get(context).textPrimary, // FIX: Use themed color
            ),
          ),
          SizedBox(height: 16),

          Container(
            decoration: BoxDecoration(
              border: Border.all(color: ThemeColor.get(context).borderSecondary), // FIX: Use themed color
              borderRadius: BorderRadius.circular(12),
            ),
            child: DropdownButtonFormField<LibyanCity>(
              value: _selectedCity,
              hint: Text(
                '--- اختر مدينتك ---',
                style: TextStyle(color: ThemeColor.get(context).textMuted), // FIX: Use themed color
              ),
              decoration: InputDecoration(
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              items: _libyanCities.map((LibyanCity city) {
                return DropdownMenuItem<LibyanCity>(
                  value: city,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          city.nameArabic,
                          style: TextStyle(
                            fontSize: 16,
                            color: ThemeColor.get(context).textPrimary, // FIX: Use themed color
                          ),
                        ),
                      ),
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: ThemeColor.get(context).brandSecondary.withValues(alpha: 0.3), // FIX: Use themed color
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          '${city.deliveryCost.toStringAsFixed(0)} د.ل',
                          style: TextStyle(
                            fontSize: 12,
                            color: ThemeColor.get(context).brandPrimary, // FIX: Use themed color
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (LibyanCity? newCity) async {
                setState(() {
                  _selectedCity = newCity;
                });
                if (newCity != null) {
                  await _updateShippingCostForSelectedCity(newCity);
                  setState(() {});
                }
              },
            ),
          ),

          if (_selectedCity != null) ...[
            SizedBox(height: 16),
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: ThemeColor.get(context).successColor.withValues(alpha: 0.1), // FIX: Use themed color
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: ThemeColor.get(context).successColor.withValues(alpha: 0.3)), // FIX: Use themed color
              ),
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: ThemeColor.get(context).successColor, size: 20), // FIX: Use themed color
                  SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'تم تحديد ${_selectedCity!.nameArabic} - ${_selectedCity!.deliveryCost.toStringAsFixed(0)} د.ل',
                      style: TextStyle(
                        color: ThemeColor.get(context).successColor, // FIX: Use themed color
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Build cancel button
  Widget _buildCancelButton() {
    return Container(
      width: double.infinity,
      height: 50,
      child: OutlinedButton(
        onPressed: () => Navigator.pop(context),
        style: OutlinedButton.styleFrom(
          side: BorderSide(color: ThemeColor.get(context).textSecondary, width: 2), // DISTINCT: Subdued border for shipping
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Text(
          "إلغاء",
          style: TextStyle(
            color: ThemeColor.get(context).textSecondary, // DISTINCT: Matching text color for shipping
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }




  /// Update shipping cost based on selected Libyan city
  Future<void> _updateShippingCostForSelectedCity(LibyanCity city) async {
    print('=== Updating Shipping Cost for Selected City: ${city.nameArabic} ===');

    final deliveryService = LibyanDeliveryService();
    await deliveryService.updateShippingCostForCity(city.nameArabic);

    print('✅ Updated shipping cost for ${city.nameArabic}');
  }

  /// Get dynamic title based on shipping status
  String _getDynamicTitle() {
    final checkoutSession = CheckoutSession.getInstance;

    // If a city is selected and shipping cost is available
    if (_selectedCity != null && checkoutSession.shippingType != null) {
      final shippingCost = checkoutSession.shippingType!.cost;
      if (shippingCost.isNotEmpty && shippingCost != "0") {
        return "الشحن إلى ${_selectedCity!.nameArabic}: ${_selectedCity!.deliveryCost.toStringAsFixed(0)} د.ل";
      } else {
        // City selected but no valid shipping cost (error scenario)
        return "الشحن غير متاح لـ ${_selectedCity!.nameArabic}";
      }
    }

    // No city selected - default prompt
    return "اختر مدينة الشحن";
  }


}
