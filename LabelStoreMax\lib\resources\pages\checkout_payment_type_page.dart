//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/app/models/checkout_session.dart';
import '/app/models/payment_type.dart';
import '/bootstrap/helpers.dart' as helpers;
import '/resources/widgets/safearea_widget.dart';
import '/resources/widgets/custom_flow_app_bar.dart';

import 'package:nylo_framework/nylo_framework.dart';

class CheckoutPaymentTypePage extends NyStatefulWidget {
  static RouteView path =
      ("/checkout-payment-type", (_) => CheckoutPaymentTypePage());

  CheckoutPaymentTypePage({super.key})
      : super(child: () => _CheckoutPaymentTypePageState());
}

class _CheckoutPaymentTypePageState extends NyPage<CheckoutPaymentTypePage> {
  List<PaymentType?> _paymentTypes = [];

  @override
  get init => () async {
        super.init();

        _paymentTypes = await helpers.getPaymentTypes();

        if (_paymentTypes.isEmpty &&
            getEnv('APP_DEBUG', defaultValue: false) == true) {
          NyLogger.info(
              'You have no payment methods set. Visit the WooSignal dashboard (https://woosignal.com/dashboard) to set a payment method.');
        }

        if (CheckoutSession.getInstance.paymentType == null) {
          if (_paymentTypes.isNotEmpty) {
            CheckoutSession.getInstance.paymentType = _paymentTypes.firstWhere(
                (paymentType) => paymentType?.id == 20,
                orElse: () => _paymentTypes.first);
          }
        }
      };

  @override
  Widget view(BuildContext context) {
    return Scaffold(
      backgroundColor: helpers.ThemeColor.get(context).backgroundContainer, // FIX: Use themed color
      appBar: CustomFlowAppBar(
        title: "طريقة الدفع",
      ),
      body: SafeAreaWidget(
        child: _buildBeautifulPaymentPage(),
      ),
    );
  }

  /// Build the redesigned payment page matching payment method ex.jpg
  Widget _buildBeautifulPaymentPage() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Payment Methods Section Header
          Padding(
            padding: EdgeInsets.symmetric(vertical: 16),
            child: Text(
              'طرق الدفع',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: helpers.ThemeColor.get(context).textPrimary,
              ),
            ),
          ),

          // Payment Methods List
          _paymentTypes.isEmpty ? _buildNoPaymentMethods() : _buildPaymentMethodsList(),

          SizedBox(height: 24),

          // Add New Card Button
          _buildAddNewCardButton(),
        ],
      ),
    );
  }



  /// Build no payment methods message
  Widget _buildNoPaymentMethods() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: helpers.ThemeColor.get(context).shadowLight,
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            Icons.payment_outlined,
            size: 48,
            color: helpers.ThemeColor.get(context).textMuted,
          ),
          SizedBox(height: 16),
          Text(
            "لا توجد طرق دفع متاحة",
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: helpers.ThemeColor.get(context).textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 8),
          Text(
            "No payment methods are available",
            style: TextStyle(
              fontSize: 14,
              color: helpers.ThemeColor.get(context).textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Build payment methods list with new card-based design
  Widget _buildPaymentMethodsList() {
    return Column(
      children: _paymentTypes.map((paymentType) => _buildPaymentMethodCard(paymentType!)).toList(),
    );
  }

  /// Build individual payment method item
  Widget _buildPaymentMethodItem(PaymentType paymentType) {
    bool isSelected = CheckoutSession.getInstance.paymentType?.id == paymentType.id;

    return Container(
      margin: EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: isSelected ? helpers.ThemeColor.get(context).successColor.withValues(alpha: 0.1) : helpers.ThemeColor.get(context).backgroundContainer, // FIX: Use themed colors
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected ? helpers.ThemeColor.get(context).successColor : helpers.ThemeColor.get(context).borderSecondary, // FIX: Use themed colors
          width: isSelected ? 2 : 1,
        ),
      ),
      child: ListTile(
        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: helpers.ThemeColor.get(context).surfaceElevated,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: helpers.ThemeColor.get(context).shadowLight,
                blurRadius: 4,
                offset: Offset(0, 2),
              ),
            ],
          ),
          padding: EdgeInsets.all(8),
          child: paymentType.assetImage.startsWith('http')
            ? Image.network(
                paymentType.assetImage,
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) => Icon(Icons.payment, color: helpers.ThemeColor.get(context).brandPrimary), // FIX: Use themed color
              )
            : Image.asset(
                helpers.getImageAsset(paymentType.assetImage),
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) => Icon(Icons.payment, color: helpers.ThemeColor.get(context).brandPrimary), // FIX: Use themed color
              ),
        ),
        title: Text(
          paymentType.desc,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: helpers.ThemeColor.get(context).textPrimary, // FIX: Use themed color
          ),
        ),
        trailing: isSelected
          ? Icon(Icons.check_circle, color: helpers.ThemeColor.get(context).successColor, size: 24) // FIX: Use themed color
          : Icon(Icons.radio_button_unchecked, color: helpers.ThemeColor.get(context).textMuted, size: 24), // FIX: Use themed color
        onTap: () {
          setState(() {
            CheckoutSession.getInstance.paymentType = paymentType;
          });
          Navigator.pop(context);
        },
      ),
    );
  }

  /// Build cancel button
  Widget _buildCancelButton() {
    return Container(
      width: double.infinity,
      height: 50,
      child: OutlinedButton(
        onPressed: () => Navigator.pop(context),
        style: OutlinedButton.styleFrom(
          side: BorderSide(color: helpers.ThemeColor.get(context).brandPrimary, width: 2), // FIX: Use themed color
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Text(
          "إلغاء",
          style: TextStyle(
            color: helpers.ThemeColor.get(context).brandPrimary, // FIX: Use themed color
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
}
