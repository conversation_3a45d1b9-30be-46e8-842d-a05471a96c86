//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/app/models/checkout_session.dart';
import '/app/models/payment_type.dart';
import '/bootstrap/helpers.dart' as helpers;
import '/resources/widgets/safearea_widget.dart';
import '/resources/widgets/custom_flow_app_bar.dart';

import 'package:nylo_framework/nylo_framework.dart';

class CheckoutPaymentTypePage extends NyStatefulWidget {
  static RouteView path =
      ("/checkout-payment-type", (_) => CheckoutPaymentTypePage());

  CheckoutPaymentTypePage({super.key})
      : super(child: () => _CheckoutPaymentTypePageState());
}

class _CheckoutPaymentTypePageState extends NyPage<CheckoutPaymentTypePage> {
  List<PaymentType?> _paymentTypes = [];

  @override
  get init => () async {
        super.init();

        _paymentTypes = await helpers.getPaymentTypes();

        if (_paymentTypes.isEmpty &&
            getEnv('APP_DEBUG', defaultValue: false) == true) {
          NyLogger.info(
              'You have no payment methods set. Visit the WooSignal dashboard (https://woosignal.com/dashboard) to set a payment method.');
        }

        if (CheckoutSession.getInstance.paymentType == null) {
          if (_paymentTypes.isNotEmpty) {
            CheckoutSession.getInstance.paymentType = _paymentTypes.firstWhere(
                (paymentType) => paymentType?.id == 20,
                orElse: () => _paymentTypes.first);
          }
        }
      };

  @override
  Widget view(BuildContext context) {
    return Scaffold(
      backgroundColor: helpers.ThemeColor.get(context).backgroundContainer, // FIX: Use themed color
      appBar: CustomFlowAppBar(
        title: "طريقة الدفع",
      ),
      body: SafeAreaWidget(
        child: _buildBeautifulPaymentPage(),
      ),
    );
  }

  /// Build the redesigned payment page matching payment method ex.jpg
  Widget _buildBeautifulPaymentPage() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Payment Methods Section Header
          Padding(
            padding: EdgeInsets.symmetric(vertical: 16),
            child: Text(
              'طرق الدفع',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: helpers.ThemeColor.get(context).textPrimary,
              ),
            ),
          ),

          // Payment Methods List
          _paymentTypes.isEmpty ? _buildNoPaymentMethods() : _buildPaymentMethodsList(),

          SizedBox(height: 24),

          // Add New Card Button
          _buildAddNewCardButton(),
        ],
      ),
    );
  }



  /// Build no payment methods message
  Widget _buildNoPaymentMethods() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: helpers.ThemeColor.get(context).shadowLight,
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            Icons.payment_outlined,
            size: 48,
            color: helpers.ThemeColor.get(context).textMuted,
          ),
          SizedBox(height: 16),
          Text(
            "لا توجد طرق دفع متاحة",
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: helpers.ThemeColor.get(context).textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 8),
          Text(
            "No payment methods are available",
            style: TextStyle(
              fontSize: 14,
              color: helpers.ThemeColor.get(context).textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Build payment methods list with new card-based design
  Widget _buildPaymentMethodsList() {
    return Column(
      children: _paymentTypes.map((paymentType) => _buildPaymentMethodCard(paymentType!)).toList(),
    );
  }

  /// Build individual payment method card matching the design
  Widget _buildPaymentMethodCard(PaymentType paymentType) {
    bool isSelected = CheckoutSession.getInstance.paymentType?.id == paymentType.id;

    return Container(
      margin: EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isSelected ? helpers.ThemeColor.get(context).successColor : helpers.ThemeColor.get(context).borderSecondary,
          width: isSelected ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: helpers.ThemeColor.get(context).shadowLight,
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: EdgeInsets.all(16),
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: helpers.ThemeColor.get(context).surfaceElevated,
            borderRadius: BorderRadius.circular(12),
          ),
          padding: EdgeInsets.all(8),
          child: _buildPaymentIcon(paymentType),
        ),
        title: Text(
          paymentType.desc,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: helpers.ThemeColor.get(context).textPrimary,
          ),
        ),
        trailing: Radio<int>(
          value: paymentType.id,
          groupValue: CheckoutSession.getInstance.paymentType?.id,
          onChanged: (value) {
            setState(() {
              CheckoutSession.getInstance.paymentType = paymentType;
            });
            Navigator.pop(context);
          },
          activeColor: helpers.ThemeColor.get(context).successColor,
        ),
        onTap: () {
          setState(() {
            CheckoutSession.getInstance.paymentType = paymentType;
          });
          Navigator.pop(context);
        },
      ),
    );
  }

  /// Build payment icon widget
  Widget _buildPaymentIcon(PaymentType paymentType) {
    if (paymentType.assetImage.startsWith('http')) {
      return Image.network(
        paymentType.assetImage,
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) => Icon(
          Icons.payment,
          color: helpers.ThemeColor.get(context).brandPrimary,
          size: 24,
        ),
      );
    } else {
      return Image.asset(
        helpers.getImageAsset(paymentType.assetImage),
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) => Icon(
          Icons.payment,
          color: helpers.ThemeColor.get(context).brandPrimary,
          size: 24,
        ),
      );
    }
  }
}
