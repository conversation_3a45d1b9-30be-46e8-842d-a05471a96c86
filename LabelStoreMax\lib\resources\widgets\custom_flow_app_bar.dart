import 'package:flutter/material.dart';
import '/bootstrap/helpers.dart';

/// Custom app bar widget for payment and shipping flow pages
/// Matches the design specifications from payment method ex.jpg and shipping ex 1.png
class CustomFlowAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final Widget? leading;
  final List<Widget>? actions;
  final bool automaticallyImplyLeading;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double elevation;

  const CustomFlowAppBar({
    Key? key,
    required this.title,
    this.leading,
    this.actions,
    this.automaticallyImplyLeading = true,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation = 0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(
        title,
        style: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: foregroundColor ?? Theme.of(context).colorScheme.onSurface,
        ),
        textAlign: TextAlign.center,
      ),
      centerTitle: true,
      backgroundColor: backgroundColor ?? Theme.of(context).scaffoldBackgroundColor,
      foregroundColor: foregroundColor ?? Theme.of(context).colorScheme.onSurface,
      elevation: elevation,
      leading: leading ?? (automaticallyImplyLeading 
        ? IconButton(
            icon: Icon(
              Icons.arrow_back_ios,
              size: 20,
              color: foregroundColor ?? Theme.of(context).colorScheme.onSurface,
            ),
            onPressed: () => Navigator.of(context).pop(),
          )
        : null),
      actions: actions,
      // Remove default shadow and use custom styling
      shadowColor: Colors.transparent,
      surfaceTintColor: Colors.transparent,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
